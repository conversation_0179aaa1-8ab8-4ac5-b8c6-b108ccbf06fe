import { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Slider } from '@/components/ui/slider';
import { ArrowLeft, Play, Pause, Download, Volume2, SkipBack, SkipForward, Video, Loader2 } from 'lucide-react';

const VoicePlayback = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { story, prompt, genre, tone, selectedVoice } = location.state || {};
  
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration] = useState(180); // 3 minutes mock duration
  const [volume, setVolume] = useState([75]);
  const [isGenerating, setIsGenerating] = useState(true);
  const [audioGenerated, setAudioGenerated] = useState(false);

  useEffect(() => {
    const generateAudio = async () => {
      if (story && selectedVoice) {
        const audioBlob = await generateAudio(story, selectedVoice.id);
        const audioUrl = URL.createObjectURL(audioBlob);
        setAudioGenerated(true);
        setIsGenerating(false);
      }
    };
    generateAudio();
  }, [story, selectedVoice]);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isPlaying && audioGenerated) {
      interval = setInterval(() => {
        setCurrentTime(prev => {
          if (prev >= duration) {
            setIsPlaying(false);
            return duration;
          }
          return prev + 1;
        });
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isPlaying, duration, audioGenerated]);

  const handlePlayPause = () => {
    if (!audioGenerated) return;
    setIsPlaying(!isPlaying);
  };

  const handleSeek = (newTime: number) => {
    setCurrentTime(newTime);
  };

  const handleSkip = (seconds: number) => {
    const newTime = Math.max(0, Math.min(duration, currentTime + seconds));
    setCurrentTime(newTime);
  };

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const handleContinueToVideo = () => {
    navigate('/character-selection', {
      state: {
        story,
        prompt,
        genre,
        tone,
        selectedVoice,
        audioUrl: '/audio/generated-story.mp3'
      }
    });
  };

  const handleDownload = () => {
    // Simulate download
    const link = document.createElement('a');
    link.href = '/audio/generated-story.mp3';
    link.download = `story-${Date.now()}.mp3`;
    link.click();
  };

  return (
    <div className="min-h-screen bg-gradient-story">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Button variant="ghost" onClick={() => navigate('/voice-selection')}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-magical bg-clip-text text-transparent">
                Your Story Narration
              </h1>
              <p className="text-muted-foreground">Listen to your tale brought to life</p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Audio Player */}
          <div className="lg:col-span-2">
            <Card className="story-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Volume2 className="h-5 w-5 text-primary" />
                  Audio Player
                </CardTitle>
                <CardDescription>
                  Narrated by {selectedVoice?.name || 'AI Voice'}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {isGenerating && (
                  <div className="flex flex-col items-center justify-center py-12 space-y-4">
                    <div className="relative">
                      <Loader2 className="h-8 w-8 text-primary animate-spin" />
                    </div>
                    <p className="text-muted-foreground">Generating your audio narration...</p>
                    <Progress value={67} className="w-64" />
                  </div>
                )}

                {audioGenerated && (
                  <>
                    {/* Progress Bar */}
                    <div className="space-y-2">
                      <Slider
                        value={[currentTime]}
                        max={duration}
                        step={1}
                        onValueChange={([value]) => handleSeek(value)}
                        className="w-full"
                      />
                      <div className="flex justify-between text-sm text-muted-foreground">
                        <span>{formatTime(currentTime)}</span>
                        <span>{formatTime(duration)}</span>
                      </div>
                    </div>

                    {/* Controls */}
                    <div className="flex items-center justify-center gap-4">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleSkip(-15)}
                      >
                        <SkipBack className="h-4 w-4" />
                        15s
                      </Button>

                      <Button
                        variant="magical"
                        size="lg"
                        onClick={handlePlayPause}
                        className="rounded-full w-16 h-16"
                      >
                        {isPlaying ? (
                          <Pause className="h-8 w-8" />
                        ) : (
                          <Play className="h-8 w-8 ml-1" />
                        )}
                      </Button>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleSkip(15)}
                      >
                        15s
                        <SkipForward className="h-4 w-4" />
                      </Button>
                    </div>

                    {/* Volume Control */}
                    <div className="flex items-center gap-4">
                      <Volume2 className="h-4 w-4 text-muted-foreground" />
                      <Slider
                        value={volume}
                        max={100}
                        step={1}
                        onValueChange={setVolume}
                        className="flex-1"
                      />
                      <span className="text-sm text-muted-foreground w-12">
                        {volume[0]}%
                      </span>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex gap-3 pt-4">
                      <Button
                        onClick={handleDownload}
                        variant="outline"
                        className="flex-1"
                      >
                        <Download className="h-4 w-4" />
                        Download MP3
                      </Button>
                      
                      <Button
                        onClick={handleContinueToVideo}
                        variant="magical"
                        className="flex-1"
                      >
                        <Video className="h-4 w-4" />
                        Continue to Video
                      </Button>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Story Info */}
          <div className="space-y-6">
            {/* Voice Info */}
            {selectedVoice && (
              <Card className="story-card">
                <CardHeader>
                  <CardTitle className="text-lg">Voice Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-semibold">{selectedVoice.name}</h4>
                    <p className="text-sm text-muted-foreground">
                      {selectedVoice.description}
                    </p>
                  </div>
                  
                  <div className="flex flex-wrap gap-1">
                    {selectedVoice.tone.map((t) => (
                      <Badge key={t} variant="outline" className="text-xs">
                        {t}
                      </Badge>
                    ))}
                  </div>

                  <div className="text-sm text-muted-foreground">
                    <p><strong>Gender:</strong> {selectedVoice.gender}</p>
                    <p><strong>Age:</strong> {selectedVoice.age}</p>
                    <p><strong>Accent:</strong> {selectedVoice.accent}</p>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Story Info */}
            <Card className="story-card">
              <CardHeader>
                <CardTitle className="text-lg">Story Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-2">
                  {genre && <Badge variant="secondary">{genre}</Badge>}
                  {tone && <Badge variant="outline">{tone}</Badge>}
                </div>
                
                <div className="text-sm space-y-2">
                  <p><strong>Duration:</strong> ~{Math.floor(duration / 60)} minutes</p>
                  <p><strong>Words:</strong> ~{story?.split(' ').length || 0}</p>
                  <p><strong>Generated:</strong> {new Date().toLocaleDateString()}</p>
                </div>
              </CardContent>
            </Card>

            {/* Story Text Preview */}
            <Card className="story-card max-h-96 overflow-hidden">
              <CardHeader>
                <CardTitle className="text-lg">Story Text</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-sm text-muted-foreground line-clamp-12 leading-relaxed">
                  {story}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VoicePlayback;
