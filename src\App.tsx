import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Dashboard from "./pages/Dashboard";
import CreateStory from "./pages/CreateStory";
import VoiceSelection from "./pages/VoiceSelection";
import VoicePlayback from "./pages/VoicePlayback";
import CharacterSelection from "./pages/CharacterSelection";
import VideoPreview from "./pages/VideoPreview";
import NotFound from "./pages/NotFound";
import Settings from "./pages/Settings";
import Login from "./pages/Login";
import Signup from "./pages/Signup";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/create" element={<CreateStory />} />
          <Route path="/voice-selection" element={<VoiceSelection />} />
          <Route path="/voice-playback" element={<VoicePlayback />} />
          <Route path="/character-selection" element={<CharacterSelection />} />
          <Route path="/video-preview" element={<VideoPreview />} />
          <Route path="/settings" element={<Settings />} />
          <Route path="/login" element={<Login />} />
          <Route path="/signup" element={<Signup />} />
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
