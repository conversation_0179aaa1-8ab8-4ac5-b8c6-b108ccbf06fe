import { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { ArrowLeft, Download, Play, Pause, RotateCcw, Home, Loader2 } from 'lucide-react';

const VideoPreview = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { story, selectedVoice, selectedCharacter, selectedTheme, genre, tone } = location.state || {};
  
  const [isGenerating, setIsGenerating] = useState(true);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [videoGenerated, setVideoGenerated] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);

  useEffect(() => {
    // Simulate video generation process
    const interval = setInterval(() => {
      setGenerationProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsGenerating(false);
          setVideoGenerated(true);
          return 100;
        }
        return prev + 2;
      });
    }, 200);

    return () => clearInterval(interval);
  }, []);

  const handleDownload = () => {
    // Simulate download
    const link = document.createElement('a');
    link.href = '/video/generated-story.mp4';
    link.download = `story-video-${Date.now()}.mp4`;
    link.click();
  };

  const handleRegenerate = () => {
    setIsGenerating(true);
    setVideoGenerated(false);
    setGenerationProgress(0);
  };

  return (
    <div className="min-h-screen bg-gradient-story">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Button variant="ghost" onClick={() => navigate('/character-selection')}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-magical bg-clip-text text-transparent">
                Your Story Video
              </h1>
              <p className="text-muted-foreground">Watch your story come to life</p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Video Player */}
          <div className="lg:col-span-2">
            <Card className="story-card">
              <CardHeader>
                <CardTitle>Story Video</CardTitle>
                <CardDescription>
                  {selectedCharacter?.name} in {selectedTheme?.name}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isGenerating && (
                  <div className="aspect-video bg-gradient-story rounded-lg flex flex-col items-center justify-center space-y-6">
                    <Loader2 className="h-12 w-12 text-primary animate-spin" />
                    <div className="text-center space-y-2">
                      <p className="text-lg font-medium">Generating your video...</p>
                      <p className="text-sm text-muted-foreground">Creating scenes and animations</p>
                    </div>
                    <div className="w-64 space-y-2">
                      <Progress value={generationProgress} />
                      <p className="text-xs text-center text-muted-foreground">
                        {generationProgress}% complete
                      </p>
                    </div>
                  </div>
                )}

                {videoGenerated && (
                  <div className="space-y-4">
                    <div className="aspect-video bg-gradient-magical rounded-lg flex items-center justify-center relative overflow-hidden">
                      <div className="absolute inset-0 bg-black/20" />
                      <Button
                        variant="magical"
                        size="lg"
                        onClick={() => setIsPlaying(!isPlaying)}
                        className="rounded-full w-20 h-20 relative z-10"
                      >
                        {isPlaying ? (
                          <Pause className="h-10 w-10" />
                        ) : (
                          <Play className="h-10 w-10 ml-1" />
                        )}
                      </Button>
                    </div>

                    <div className="flex gap-3">
                      <Button onClick={handleDownload} variant="outline" className="flex-1">
                        <Download className="h-4 w-4" />
                        Download Video
                      </Button>
                      <Button onClick={handleRegenerate} variant="outline">
                        <RotateCcw className="h-4 w-4" />
                        Regenerate
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Details Sidebar */}
          <div className="space-y-6">
            <Card className="story-card">
              <CardHeader>
                <CardTitle>Video Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-2">
                  {genre && <Badge variant="secondary">{genre}</Badge>}
                  {tone && <Badge variant="outline">{tone}</Badge>}
                </div>
                
                <div className="space-y-2 text-sm">
                  <p><strong>Character:</strong> {selectedCharacter?.name}</p>
                  <p><strong>Theme:</strong> {selectedTheme?.name}</p>
                  <p><strong>Voice:</strong> {selectedVoice?.name}</p>
                  <p><strong>Duration:</strong> ~3 minutes</p>
                </div>
              </CardContent>
            </Card>

            <div className="flex flex-col gap-3">
              <Button variant="magical" className="w-full" onClick={() => navigate('/')}>
                <Home className="h-4 w-4" />
                Back to Dashboard
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VideoPreview;