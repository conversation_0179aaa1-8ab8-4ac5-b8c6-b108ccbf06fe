import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { dummyStories } from "@/data/dummy-data";
import {
  PlusCircle,
  Search,
  Sparkles,
  Clock,
  BookOpen,
  Mic,
  Video,
  Settings,
  TreePine,
  Leaf,
} from "lucide-react";

const Dashboard = () => {
  const [searchQuery, setSearchQuery] = useState("");

  const filteredStories = dummyStories.filter(
    (story) =>
      story.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      story.genre.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const formatDate = (date: Date) => {
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-r from-emerald-100 via-green-100 to-teal-100 backdrop-blur-md shadow-lg w-full text-foreground">
      {/* Header */}
      <div className="absolute top-8 left-0 w-full h-fit overflow-hidden">
        <img className="h-full w-full z-o " src="/images/jungle.jpg" />
      </div>
      <header className="relative border-b border-green-200/50 bg-gradient-to-r from-emerald-100 via-green-100 to-teal-100 backdrop-blur-md shadow-lg overflow-hidden">
      {/* Animated vine decorations */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Left vine */}
        <div className="absolute left-0 top-0 h-full w-8">
          <div className="vine-creeper animate-vine-grow opacity-60">
            🌿🍃🌿🍃🌿
          </div>
        </div>
        
        {/* Right vine */}
        <div className="absolute right-0 top-0 h-full w-8">
          <div className="vine-creeper animate-vine-grow-reverse opacity-60">
            🍃🌿🍃🌿🍃
          </div>
        </div>

        {/* Floating leaves */}
        <div className="absolute top-2 left-1/4 animate-float-slow">🍃</div>
        <div className="absolute top-4 right-1/3 animate-float-delayed">🌿</div>
        <div className="absolute top-1 left-3/4 animate-bounce-gentle">🦋</div>
      </div>

      <div className="container mx-auto px-4 py-5 relative z-10">
        <div className="flex items-center justify-between">
          {/* Left side: Logo and Title */}
          <div className="flex items-center gap-3">
            <div className="relative">
              <TreePine className="h-8 w-8 text-green-600 animate-sway" />
              <Leaf className="absolute -top-1 -right-1 h-4 w-4 text-emerald-500 animate-spin-slow" />
            </div>
            <h1 className="text-3xl font-extrabold bg-gradient-to-r from-green-600 via-emerald-500 to-teal-600 bg-clip-text text-transparent tracking-wide font-serif">
              Story Telling
              <span className="ml-1 animate-pulse">🌴</span>
            </h1>
          </div>

          {/* Right side: Settings */}
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              className="rounded-full hover:bg-green-100 transition-all duration-300 hover:scale-105"
              asChild
            >
              <Link to="/settings">
                <Settings className="h-4 w-4 mr-1 text-emerald-600 animate-spin-slow" />
                <span className="font-medium text-green-700">Settings</span>
              </Link>
            </Button>
          </div>
        </div>
      </div>

      {/* Bottom jungle ground effect */}
      <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-green-400 via-emerald-400 to-teal-400 opacity-80"></div>
    </header>

      <div className="container mx-auto px-4 py-12 relative z-10">
        {/* Welcome Section */}
        <div className="text-center mb-16">
          <h2 className="text-5xl font-bold tracking-tight mb-4 text-green-100 dark:text-white">
            Welcome to Your Story Universe
          </h2>
          <p className="text-lg text-white max-w-2xl text-shadow-lg drop-shadow-lg mx-auto mb-10">

            Where imagination meets AI magic. Create, narrate, and bring your
            stories to life with stunning visuals.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button variant="hero" size="xl" className="animate-float" asChild>
              <Link to="/create">
                <PlusCircle className="h-6 w-6 mr-2" />
                Create New Story
              </Link>
            </Button>
            <Button variant="warm" size="xl" asChild>
              <Link to="/gallery">
                <BookOpen className="h-6 w-6 mr-2" />
                Story Gallery
              </Link>
            </Button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-16">
          <Card className="story-card shadow-md hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Stories Created
              </CardTitle>
              <BookOpen className="h-4 w-4 text-primary" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-primary">
                {dummyStories.length}
              </div>
            </CardContent>
          </Card>

          <Card className="story-card shadow-md hover:shadow-lg transition-shadow">
            <CardHeader className="flex items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Voice Narrations
              </CardTitle>
              <Mic className="h-4 w-4 text-story-pink" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-story-pink">
                {dummyStories.filter((s) => s.voiceId).length}
              </div>
            </CardContent>
          </Card>

          <Card className="story-card shadow-md hover:shadow-lg transition-shadow">
            <CardHeader className="flex items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Video Stories
              </CardTitle>
              <Video className="h-4 w-4 text-story-blue" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-story-blue">
                {dummyStories.filter((s) => s.characterId && s.themeId).length}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Stories */}
        <section className="mb-10">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
            <h3 className="text-2xl font-semibold mb-4 sm:mb-0">
              Your Recent Stories
            </h3>
            <div className="relative w-full sm:w-80">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search stories..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredStories.map((story, index) => (
              <Card
                key={story.id}
                className="story-card story-reveal transition-transform transform hover:-translate-y-1"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <CardHeader>
                  <div className="flex justify-between items-start mb-2">
                    <Badge variant="secondary">{story.genre}</Badge>
                    <div className="flex items-center text-sm text-muted-foreground">
                      <Clock className="h-3 w-3 mr-1" />
                      {formatDate(story.timestamp)}
                    </div>
                  </div>
                  <CardTitle className="text-lg mb-2">{story.title}</CardTitle>
                  <CardDescription className="text-sm line-clamp-3">
                    {story.content.substring(0, 120)}...
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex justify-between text-sm text-muted-foreground mb-4">
                    <span>{story.wordCount} words</span>
                    <div className="flex gap-1">
                      {story.voiceId && (
                        <Badge variant="outline" className="text-xs">
                          <Mic className="h-3 w-3 mr-1" />
                          Voice
                        </Badge>
                      )}
                      {story.characterId && story.themeId && (
                        <Badge variant="outline" className="text-xs">
                          <Video className="h-3 w-3 mr-1" />
                          Video
                        </Badge>
                      )}
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="magical"
                      className="flex-1"
                      asChild
                    >
                      <Link to={`/story/${story.id}`}>View Story</Link>
                    </Button>
                    <Button size="sm" variant="outline" asChild>
                      <Link to={`/story/${story.id}/continue`}>Continue</Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredStories.length === 0 && (
            <div className="text-center py-16">
              <Sparkles className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">No stories found</h3>
              <p className="text-muted-foreground mb-6">
                {searchQuery
                  ? "Try adjusting your search terms"
                  : "Create your first magical story!"}
              </p>
              <Button variant="magical" asChild>
                <Link to="/create">
                  <PlusCircle className="h-4 w-4 mr-2" />
                  Create New Story
                </Link>
              </Button>
            </div>
          )}
        </section>
      </div>
    </div>
  );
};

export default Dashboard;
