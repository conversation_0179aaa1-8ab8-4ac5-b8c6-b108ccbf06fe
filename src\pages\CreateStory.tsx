import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Loader2, <PERSON>rk<PERSON>, Wand2, ArrowLeft, RefreshCw } from 'lucide-react';
import { genreOptions, toneOptions } from '@/data/dummy-data';

const CreateStory = () => {
  const navigate = useNavigate();
  const [prompt, setPrompt] = useState('');
  const [genre, setGenre] = useState('');
  const [tone, setTone] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedStory, setGeneratedStory] = useState('');

  const handleGenerate = async () => {
    if (!prompt.trim()) return;
    
    setIsGenerating(true);
    
    // Simulate AI story generation
    setTimeout(() => {
      const mockStory = `In a realm where ${tone.toLowerCase()} magic flows through every whisper of the wind, ${prompt.toLowerCase()}. 

The journey began on a morning when the sky painted itself in shades of wonder, and the very air seemed to hum with possibility. Our protagonist discovered that the ordinary world they knew was merely a thin veil covering something far more extraordinary.

As they ventured deeper into this newfound reality, challenges arose that would test not just their courage, but their very understanding of what it means to be truly alive. Each step forward revealed new mysteries, new allies, and new truths about the power that had always slept within their heart.

The climax came when they realized that the greatest magic wasn't in the spells they could cast or the creatures they could summon, but in the connections they forged and the love they chose to share. For in the end, it wasn't the destination that mattered, but the transformation that occurred along the way.

And so, as the sun set on this chapter of their adventure, they knew it was not an ending, but a beginning—the first page of a story that would continue to unfold with each choice they made and each dream they dared to pursue.`;
      
      setGeneratedStory(mockStory);
      setIsGenerating(false);
    }, 3000);
  };

  const handleRegenerateStory = () => {
    setGeneratedStory('');
    handleGenerate();
  };

  const handleContinueToVoice = () => {
    // Navigate to voice selection with the story data
    navigate('/voice-selection', { 
      state: { 
        story: generatedStory, 
        prompt, 
        genre, 
        tone 
      } 
    });
  };

  return (
    <div className="min-h-screen bg-gradient-story">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Button variant="ghost" onClick={() => navigate('/')}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-magical bg-clip-text text-transparent">
                Create Your Story
              </h1>
              <p className="text-muted-foreground">Let AI weave your imagination into magic</p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Prompt Input Section */}
          <Card className="story-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Wand2 className="h-5 w-5 text-primary" />
                Story Prompt
              </CardTitle>
              <CardDescription>
                Describe your story idea. Be as creative and detailed as you'd like!
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <Label htmlFor="prompt">Your Story Idea</Label>
                <Textarea
                  id="prompt"
                  placeholder="A young wizard discovers a hidden library that contains books from parallel universes..."
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  className="min-h-32 mt-2"
                  disabled={isGenerating}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="genre">Genre</Label>
                  <Select value={genre} onValueChange={setGenre} disabled={isGenerating}>
                    <SelectTrigger className="mt-2">
                      <SelectValue placeholder="Select genre" />
                    </SelectTrigger>
                    <SelectContent>
                      {genreOptions.map((option) => (
                        <SelectItem key={option} value={option}>
                          {option}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="tone">Tone</Label>
                  <Select value={tone} onValueChange={setTone} disabled={isGenerating}>
                    <SelectTrigger className="mt-2">
                      <SelectValue placeholder="Select tone" />
                    </SelectTrigger>
                    <SelectContent>
                      {toneOptions.map((option) => (
                        <SelectItem key={option} value={option}>
                          {option}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex gap-2">
                {genre && <Badge variant="secondary">{genre}</Badge>}
                {tone && <Badge variant="outline">{tone}</Badge>}
              </div>

              <Button
                onClick={handleGenerate}
                disabled={!prompt.trim() || isGenerating}
                className="w-full"
                variant="magical"
                size="lg"
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" />
                    Weaving your story...
                  </>
                ) : (
                  <>
                    <Sparkles className="h-4 w-4" />
                    Generate Story
                  </>
                )}
              </Button>
            </CardContent>
          </Card>

          {/* Story Display Section */}
          <Card className="story-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Sparkles className="h-5 w-5 text-primary" />
                Generated Story
              </CardTitle>
              <CardDescription>
                Your AI-crafted tale is ready to enchant
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isGenerating && (
                <div className="flex flex-col items-center justify-center py-12 space-y-4">
                  <div className="relative">
                    <Sparkles className="h-8 w-8 text-primary animate-pulse" />
                    <div className="absolute inset-0 h-8 w-8 border-2 border-primary/30 rounded-full animate-ping" />
                  </div>
                  <p className="text-muted-foreground animate-pulse">Creating magic...</p>
                </div>
              )}

              {generatedStory && (
                <div className="space-y-6">
                  <div className="bg-muted/50 rounded-lg p-6 story-reveal">
                    <div className="prose prose-sm max-w-none">
                      {generatedStory.split('\n\n').map((paragraph, index) => (
                        <p key={index} className="mb-4 leading-relaxed">
                          {paragraph}
                        </p>
                      ))}
                    </div>
                  </div>

                  <div className="flex gap-3">
                    <Button
                      onClick={handleRegenerateStory}
                      variant="outline"
                      className="flex-1"
                    >
                      <RefreshCw className="h-4 w-4" />
                      Regenerate
                    </Button>
                    
                    <Button
                      onClick={handleContinueToVoice}
                      variant="magical"
                      className="flex-1"
                    >
                      Continue to Voice
                    </Button>
                  </div>
                </div>
              )}

              {!isGenerating && !generatedStory && (
                <div className="flex flex-col items-center justify-center py-12 text-center space-y-4">
                  <Wand2 className="h-12 w-12 text-muted-foreground/50" />
                  <p className="text-muted-foreground">
                    Enter your story prompt and click generate to begin the magic
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default CreateStory;