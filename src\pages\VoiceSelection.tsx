import { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { dummyVoices, Voice } from '@/data/dummy-data';
import { ArrowLeft, Play, Pause, Crown, Mic, Volume2, Clock } from 'lucide-react';

const VoiceSelection = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { story, prompt, genre, tone } = location.state || {};
  
  const [selectedVoice, setSelectedVoice] = useState<Voice | null>(null);
  const [playingVoice, setPlayingVoice] = useState<string | null>(null);

  const handleVoiceSelect = (voice: Voice) => {
    setSelectedVoice(voice);
  };

  const handlePlaySample = (voiceId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    
    if (playingVoice === voiceId) {
      setPlayingVoice(null);
    } else {
      setPlayingVoice(voiceId);
      // Simulate audio playing
      setTimeout(() => {
        setPlayingVoice(null);
      }, 3000);
    }
  };

  const handleContinueToPlayback = () => {
    if (!selectedVoice) return;
    
    navigate('/voice-playback', {
      state: {
        story,
        prompt,
        genre,
        tone,
        selectedVoice
      }
    });
  };

  const getToneColor = (tones: string[]) => {
    const primaryTone = tones[0].toLowerCase();
    if (['warm', 'caring', 'gentle'].includes(primaryTone)) return 'bg-story-warm/20 text-story-warm border-story-warm/30';
    if (['mysterious', 'dark', 'dramatic'].includes(primaryTone)) return 'bg-purple-100 text-purple-700 border-purple-300';
    if (['adventurous', 'heroic', 'strong'].includes(primaryTone)) return 'bg-blue-100 text-blue-700 border-blue-300';
    return 'bg-gray-100 text-gray-700 border-gray-300';
  };

  return (
    <div className="min-h-screen bg-gradient-story">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Button variant="ghost" onClick={() => navigate('/create')}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-magical bg-clip-text text-transparent">
                Choose Your Narrator
              </h1>
              <p className="text-muted-foreground">Select the perfect voice to bring your story to life</p>
            </div>
          </div>
        </div>

        {/* Story Preview */}
        {story && (
          <Card className="mb-8 bg-card/50 backdrop-blur-sm border-primary/20">
            <CardHeader>
              <CardTitle className="text-lg">Your Story Preview</CardTitle>
              <div className="flex gap-2">
                {genre && <Badge variant="secondary">{genre}</Badge>}
                {tone && <Badge variant="outline">{tone}</Badge>}
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground line-clamp-3">
                {story.substring(0, 200)}...
              </p>
            </CardContent>
          </Card>
        )}

        {/* Voice Selection Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {dummyVoices.map((voice, index) => (
            <Card
              key={voice.id}
              className={`story-card cursor-pointer transition-all duration-300 ${
                selectedVoice?.id === voice.id 
                  ? 'ring-2 ring-primary shadow-magical' 
                  : ''
              }`}
              style={{ animationDelay: `${index * 0.1}s` }}
              onClick={() => handleVoiceSelect(voice)}
            >
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Mic className="h-5 w-5 text-primary" />
                    {voice.name}
                    {voice.isPremium && (
                      <Crown className="h-4 w-4 text-story-gold" />
                    )}
                  </CardTitle>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={(e) => handlePlaySample(voice.id, e)}
                    className="shrink-0"
                  >
                    {playingVoice === voice.id ? (
                      <Pause className="h-3 w-3" />
                    ) : (
                      <Play className="h-3 w-3" />
                    )}
                  </Button>
                </div>
                <CardDescription>{voice.description}</CardDescription>
              </CardHeader>
              
              <CardContent className="space-y-4">
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <span className="capitalize">{voice.gender}</span>
                  <span>•</span>
                  <span className="capitalize">{voice.age}</span>
                  <span>•</span>
                  <span>{voice.accent}</span>
                </div>

                <div className="flex flex-wrap gap-1">
                  {voice.tone.map((t) => (
                    <Badge 
                      key={t} 
                      variant="outline" 
                      className={`text-xs ${getToneColor([t])}`}
                    >
                      {t}
                    </Badge>
                  ))}
                </div>

                {playingVoice === voice.id && (
                  <div className="flex items-center gap-2 text-sm text-primary">
                    <Volume2 className="h-4 w-4 animate-pulse" />
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      <span>Playing sample...</span>
                    </div>
                  </div>
                )}

                {selectedVoice?.id === voice.id && (
                  <div className="flex items-center gap-2 text-sm text-primary font-medium">
                    <div className="w-2 h-2 bg-primary rounded-full animate-pulse" />
                    Selected
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Continue Button */}
        <div className="flex justify-center">
          <Button
            onClick={handleContinueToPlayback}
            disabled={!selectedVoice}
            variant="magical"
            size="lg"
            className="px-12"
          >
            Convert to Voice
          </Button>
        </div>
      </div>
    </div>
  );
};

export default VoiceSelection;